#!/usr/bin/env python3
"""
Comprehensive Decoder Program
Tries multiple decoding approaches to find readable text
"""

import base64
import binascii

def decode_hex_to_binary(hex_string):
    """Convert hexadecimal escape sequences to binary representation"""
    hex_parts = hex_string.split('\\x')[1:]
    binary_string = ""
    
    for hex_part in hex_parts:
        if hex_part:
            char_code = int(hex_part, 16)
            char = chr(char_code)
            
            if char == '5':
                binary_string += '0'
            elif char == '6':
                binary_string += '1'
            elif char == ' ':
                binary_string += ' '
    
    return binary_string

def binary_to_text(binary_string):
    """Convert binary string to ASCII text"""
    binary_parts = binary_string.split(' ')
    decoded_text = ""
    
    for binary_part in binary_parts:
        if binary_part:
            try:
                char_code = int(binary_part, 2)
                if 0 <= char_code <= 127:
                    decoded_text += chr(char_code)
            except ValueError:
                continue
    
    return decoded_text

def try_hex_as_words(hex_string):
    """Try to interpret hex string as encoded words"""
    results = []
    
    # Remove spaces and try different chunk sizes
    clean_hex = hex_string.replace(' ', '')
    
    # Try 2-character chunks (standard hex bytes)
    if len(clean_hex) % 2 == 0:
        try:
            text = ""
            for i in range(0, len(clean_hex), 2):
                hex_byte = clean_hex[i:i+2]
                byte_val = int(hex_byte, 16)
                if 32 <= byte_val <= 126:  # Printable ASCII
                    text += chr(byte_val)
                else:
                    text += f"[{byte_val}]"
            if any(c.isalpha() for c in text):  # Contains letters
                results.append(("Hex bytes to ASCII", text))
        except:
            pass
    
    # Try interpreting as different encodings
    try:
        # Convert hex to bytes then try different decodings
        hex_bytes = bytes.fromhex(clean_hex)
        
        # Try UTF-8
        try:
            utf8_text = hex_bytes.decode('utf-8')
            if utf8_text.isprintable():
                results.append(("Hex to UTF-8", utf8_text))
        except:
            pass
        
        # Try Latin-1
        try:
            latin1_text = hex_bytes.decode('latin-1')
            if any(c.isalpha() for c in latin1_text):
                results.append(("Hex to Latin-1", latin1_text))
        except:
            pass
            
    except:
        pass
    
    return results

def try_reverse_operations(text):
    """Try reversing the text or other transformations"""
    results = []
    
    # Try reversing
    reversed_text = text[::-1]
    if any(c.isalpha() for c in reversed_text):
        results.append(("Reversed", reversed_text))
    
    # Try ROT13
    try:
        rot13_text = text.encode('rot13')
        results.append(("ROT13", rot13_text))
    except:
        pass
    
    return results

def main():
    # The encoded message
    encoded_message = r"\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x36\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x35\x36\x36\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36"
    
    print("=== COMPREHENSIVE DECODER ===")
    
    # Step 1: Basic decoding
    binary_representation = decode_hex_to_binary(encoded_message)
    decoded_text = binary_to_text(binary_representation)
    print(f"\nStep 1 - Binary to text: '{decoded_text}'")
    
    # Step 2: Base64 decode
    try:
        base64_decoded = base64.b64decode(decoded_text).decode('utf-8')
        print(f"Step 2 - Base64 decoded: '{base64_decoded}'")
        
        # Step 3: Try different interpretations of the hex string
        print(f"\nStep 3 - Trying different hex interpretations:")
        hex_results = try_hex_as_words(base64_decoded)
        for method, result in hex_results:
            print(f"  {method}: '{result}'")
            
            # Try transformations on each result
            transform_results = try_reverse_operations(result)
            for transform_method, transform_result in transform_results:
                print(f"    {transform_method}: '{transform_result}'")
        
        # Step 4: Try treating the hex as a different encoding entirely
        print(f"\nStep 4 - Alternative hex interpretations:")
        
        # Maybe it's not standard hex - try other patterns
        # Let's see if there are patterns in the hex string
        print(f"Hex string length: {len(base64_decoded)}")
        print(f"First 20 chars: {base64_decoded[:20]}")
        print(f"Last 20 chars: {base64_decoded[-20:]}")
        
        # Try splitting into different sized chunks
        for chunk_size in [1, 2, 4, 8, 16]:
            if len(base64_decoded) % chunk_size == 0:
                chunks = [base64_decoded[i:i+chunk_size] for i in range(0, len(base64_decoded), chunk_size)]
                print(f"  {chunk_size}-char chunks: {chunks[:5]}..." if len(chunks) > 5 else f"  {chunk_size}-char chunks: {chunks}")
        
    except Exception as e:
        print(f"Base64 decode failed: {e}")

if __name__ == "__main__":
    main()
