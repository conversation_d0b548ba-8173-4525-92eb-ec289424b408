#!/usr/bin/env python3
"""
Binary Decoder Program
Decodes hexadecimal escape sequences that represent binary data
"""

import base64

def decode_hex_to_binary(hex_string):
    """
    Convert hexadecimal escape sequences to binary representation
    Assumes \x35 = '0' and \x36 = '1' (ASCII 53 and 54)
    """
    # Remove \x prefixes and convert hex to characters
    hex_parts = hex_string.split('\\x')[1:]  # Skip first empty element
    binary_string = ""
    
    for hex_part in hex_parts:
        if hex_part:  # Skip empty parts
            # Convert hex to integer, then to character
            char_code = int(hex_part, 16)
            char = chr(char_code)
            
            # Convert '5' and '6' to '0' and '1'
            if char == '5':
                binary_string += '0'
            elif char == '6':
                binary_string += '1'
            elif char == ' ':
                binary_string += ' '  # Preserve spaces
    
    return binary_string

def binary_to_text(binary_string):
    """
    Convert binary string to ASCII text
    """
    # Split by spaces to get individual binary numbers
    binary_parts = binary_string.split(' ')
    decoded_text = ""
    
    for binary_part in binary_parts:
        if binary_part:  # Skip empty parts
            try:
                # Convert binary to integer, then to character
                char_code = int(binary_part, 2)
                if 0 <= char_code <= 127:  # Valid ASCII range
                    decoded_text += chr(char_code)
            except ValueError:
                # Skip invalid binary sequences
                continue
    
    return decoded_text

def try_direct_binary_decode(binary_string):
    """
    Try to decode binary directly as 7-bit or 8-bit ASCII
    """
    results = []

    # Remove spaces and try different groupings
    clean_binary = binary_string.replace(' ', '')

    # Try 7-bit ASCII
    if len(clean_binary) % 7 == 0:
        text_7bit = ""
        for i in range(0, len(clean_binary), 7):
            binary_chunk = clean_binary[i:i+7]
            char_code = int(binary_chunk, 2)
            if 32 <= char_code <= 126:
                text_7bit += chr(char_code)
        if text_7bit:
            results.append(("7-bit ASCII", text_7bit))

    # Try 8-bit ASCII
    if len(clean_binary) % 8 == 0:
        text_8bit = ""
        for i in range(0, len(clean_binary), 8):
            binary_chunk = clean_binary[i:i+8]
            char_code = int(binary_chunk, 2)
            if 32 <= char_code <= 126:
                text_8bit += chr(char_code)
        if text_8bit:
            results.append(("8-bit ASCII", text_8bit))

    return results

def main():
    # The encoded message
    encoded_message = r"\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x36\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x35\x36\x36\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36"
    
    print("=== Binary Decoder Program ===")
    print("\nStep 1: Converting hex escape sequences to binary...")
    
    # Step 1: Convert hex to binary representation
    binary_representation = decode_hex_to_binary(encoded_message)
    print(f"Binary representation: {binary_representation[:100]}...")  # Show first 100 chars
    
    print("\nStep 2: Converting binary to text...")
    
    # Step 2: Convert binary to text
    decoded_text = binary_to_text(binary_representation)
    
    print(f"\n=== DECODED MESSAGE ===")
    print(f"'{decoded_text}'")

    # Check if it's Base64 and decode it
    print("\nStep 3: Checking if the result is Base64 encoded...")
    try:
        # Try to decode as Base64
        base64_decoded = base64.b64decode(decoded_text).decode('utf-8')
        print(f"\n=== BASE64 DECODED ===")
        print(f"'{base64_decoded}'")

        # Step 4: Try to decode hex to text
        print("\nStep 4: Trying to decode hex string to text...")
        try:
            # Remove any non-hex characters and convert hex pairs to characters
            hex_string = base64_decoded.strip()
            if len(hex_string) % 2 == 0:  # Must be even length for hex pairs
                text_result = ""
                for i in range(0, len(hex_string), 2):
                    hex_pair = hex_string[i:i+2]
                    char_code = int(hex_pair, 16)
                    if 32 <= char_code <= 126:  # Printable ASCII range
                        text_result += chr(char_code)
                    else:
                        text_result += f"[{char_code}]"  # Show non-printable as numbers

                print(f"\n=== FINAL DECODED MESSAGE (Hex to Text) ===")
                print(f"'{text_result}'")
            else:
                print("Hex string has odd length, cannot decode to text")
        except Exception as hex_error:
            print(f"Could not decode hex to text: {hex_error}")

    except Exception as e:
        print(f"Not valid Base64 or UTF-8: {e}")

    # Try direct binary decoding approaches
    print("\n=== TRYING DIRECT BINARY DECODING ===")
    direct_results = try_direct_binary_decode(binary_representation)
    for method, result in direct_results:
        print(f"{method}: '{result}'")

    # Also try alternative interpretation (in case 5=1, 6=0)
    print("\n=== Alternative Interpretation (5=1, 6=0) ===")
    alt_binary = binary_representation.replace('0', 'temp').replace('1', '0').replace('temp', '1')
    alt_decoded = binary_to_text(alt_binary)
    print(f"'{alt_decoded}'")

    # Try direct decoding on alternative binary too
    if alt_decoded:
        print("\nTrying direct binary decoding on alternative interpretation...")
        alt_direct_results = try_direct_binary_decode(alt_binary)
        for method, result in alt_direct_results:
            print(f"Alt {method}: '{result}'")

if __name__ == "__main__":
    main()
