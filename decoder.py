#!/usr/bin/env python3
"""
Binary Decoder Program
Decodes hexadecimal escape sequences that represent binary data
"""

import base64

def decode_hex_to_binary(hex_string):
    """
    Convert hexadecimal escape sequences to binary representation
    Assumes \x35 = '0' and \x36 = '1' (ASCII 53 and 54)
    """
    # Remove \x prefixes and convert hex to characters
    hex_parts = hex_string.split('\\x')[1:]  # Skip first empty element
    binary_string = ""
    
    for hex_part in hex_parts:
        if hex_part:  # Skip empty parts
            # Convert hex to integer, then to character
            char_code = int(hex_part, 16)
            char = chr(char_code)
            
            # Convert '5' and '6' to '0' and '1'
            if char == '5':
                binary_string += '0'
            elif char == '6':
                binary_string += '1'
            elif char == ' ':
                binary_string += ' '  # Preserve spaces
    
    return binary_string

def binary_to_text(binary_string):
    """
    Convert binary string to ASCII text
    """
    # Split by spaces to get individual binary numbers
    binary_parts = binary_string.split(' ')
    decoded_text = ""
    
    for binary_part in binary_parts:
        if binary_part:  # Skip empty parts
            try:
                # Convert binary to integer, then to character
                char_code = int(binary_part, 2)
                if 0 <= char_code <= 127:  # Valid ASCII range
                    decoded_text += chr(char_code)
            except ValueError:
                # Skip invalid binary sequences
                continue
    
    return decoded_text

def main():
    # The encoded message
    encoded_message = r"\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x36\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x35\x36\x36\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36"
    
    print("=== Binary Decoder Program ===")
    print("\nStep 1: Converting hex escape sequences to binary...")
    
    # Step 1: Convert hex to binary representation
    binary_representation = decode_hex_to_binary(encoded_message)
    print(f"Binary representation: {binary_representation[:100]}...")  # Show first 100 chars
    
    print("\nStep 2: Converting binary to text...")
    
    # Step 2: Convert binary to text
    decoded_text = binary_to_text(binary_representation)
    
    print(f"\n=== DECODED MESSAGE ===")
    print(f"'{decoded_text}'")

    # Check if it's Base64 and decode it
    print("\nStep 3: Checking if the result is Base64 encoded...")
    try:
        # Try to decode as Base64
        base64_decoded = base64.b64decode(decoded_text).decode('utf-8')
        print(f"\n=== FINAL DECODED MESSAGE (Base64 decoded) ===")
        print(f"'{base64_decoded}'")
    except Exception as e:
        print(f"Not valid Base64 or UTF-8: {e}")

    # Also try alternative interpretation (in case 5=1, 6=0)
    print("\n=== Alternative Interpretation (5=1, 6=0) ===")
    alt_binary = binary_representation.replace('0', 'temp').replace('1', '0').replace('temp', '1')
    alt_decoded = binary_to_text(alt_binary)
    print(f"'{alt_decoded}'")

if __name__ == "__main__":
    main()
