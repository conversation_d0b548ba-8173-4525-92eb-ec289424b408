#!/usr/bin/env python3
"""
Pattern Analyzer - Look for different encoding patterns
"""

def analyze_original_pattern(hex_string):
    """Analyze the original hex escape sequence pattern"""
    print("=== ANALYZING ORIGINAL PATTERN ===")
    
    # Split by \x and analyze
    parts = hex_string.split('\\x')[1:]  # Skip first empty
    
    print(f"Total hex parts: {len(parts)}")
    
    # Count frequency of each hex value
    hex_counts = {}
    for part in parts:
        if part:
            hex_counts[part] = hex_counts.get(part, 0) + 1
    
    print("Hex value frequencies:")
    for hex_val, count in sorted(hex_counts.items()):
        char = chr(int(hex_val, 16)) if hex_val else '?'
        print(f"  \\x{hex_val} ('{char}'): {count} times")
    
    # Look for patterns
    print(f"\nFirst 20 hex values: {parts[:20]}")
    
    # Try different interpretations
    print(f"\n=== TRYING DIFFERENT INTERPRETATIONS ===")
    
    # What if 35 and 36 represent different binary values?
    # Or what if they represent something else entirely?
    
    # Try ASCII values directly
    ascii_result = ""
    for part in parts:
        if part and part != '20':  # Skip spaces
            try:
                ascii_result += chr(int(part, 16))
            except:
                pass
    print(f"Direct ASCII: '{ascii_result[:50]}...'")
    
    # Try treating 35/36 as different mappings
    mappings = [
        {'35': 'A', '36': 'B', '20': ' '},
        {'35': '0', '36': '1', '20': ' '},
        {'35': '1', '36': '0', '20': ' '},
        {'35': 'T', '36': 'F', '20': ' '},
        {'35': 'L', '36': 'H', '20': ' '},
    ]
    
    for i, mapping in enumerate(mappings):
        result = ""
        for part in parts:
            if part in mapping:
                result += mapping[part]
        print(f"Mapping {i+1} ({'35->'+list(mapping.keys())[0] if '35' in mapping else ''}): '{result[:50]}...'")

def try_morse_like_encoding(hex_string):
    """Try interpreting as morse-like encoding"""
    print(f"\n=== MORSE-LIKE ENCODING ===")
    
    # What if spaces separate letters and 35/36 are dots/dashes?
    parts = hex_string.split('\\x20')  # Split by spaces
    
    morse_mappings = {
        '35': '.',
        '36': '-'
    }
    
    morse_result = []
    for part in parts:
        if part:
            # Convert this part to morse
            morse_char = ""
            hex_parts = part.split('\\x')[1:]
            for hex_part in hex_parts:
                if hex_part in morse_mappings:
                    morse_char += morse_mappings[hex_part]
            if morse_char:
                morse_result.append(morse_char)
    
    print(f"Morse-like pattern: {morse_result[:10]}...")
    
    # Try to decode morse (basic morse code)
    morse_to_letter = {
        '.-': 'A', '-...': 'B', '-.-.': 'C', '-..': 'D', '.': 'E',
        '..-.': 'F', '--.': 'G', '....': 'H', '..': 'I', '.---': 'J',
        '-.-': 'K', '.-..': 'L', '--': 'M', '-.': 'N', '---': 'O',
        '.--.': 'P', '--.-': 'Q', '.-.': 'R', '...': 'S', '-': 'T',
        '..-': 'U', '...-': 'V', '.--': 'W', '-..-': 'X', '-.--': 'Y',
        '--..': 'Z'
    }
    
    decoded_morse = ""
    for morse_char in morse_result:
        if morse_char in morse_to_letter:
            decoded_morse += morse_to_letter[morse_char]
        else:
            decoded_morse += f"[{morse_char}]"
    
    print(f"Morse decoded: '{decoded_morse}'")

def main():
    # The encoded message
    encoded_message = r"\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x35\x36\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x35\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x36\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x35\x36\x36\x20\x35\x35\x36\x36\x35\x35\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x36\x35\x36\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x35\x36\x36\x36\x20\x35\x35\x36\x36\x35\x36\x35\x35\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x35\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x36\x36\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x36\x36\x36\x36\x35\x35\x35\x20\x35\x36\x35\x36\x36\x35\x35\x36\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x35\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x35\x35\x35\x36\x35\x35\x20\x35\x36\x35\x36\x35\x35\x35\x36\x20\x35\x35\x36\x36\x35\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x36\x35\x20\x35\x35\x36\x36\x35\x35\x36\x35\x20\x35\x36\x35\x36\x36\x35\x36\x35\x20\x35\x36\x36\x35\x36\x36\x35\x36\x20\x35\x36\x35\x35\x36\x36\x35\x36\x20\x35\x36\x36\x36\x35\x36\x36\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36\x20\x35\x35\x36\x36\x36\x36\x35\x36"
    
    analyze_original_pattern(encoded_message)
    try_morse_like_encoding(encoded_message)

if __name__ == "__main__":
    main()
